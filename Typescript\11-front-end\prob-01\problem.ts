// Front-End Senior Developer Problem: Virtual DOM and State Management
// Problem: Implement a simplified Virtual DOM system with state management
// This is a practice problem - implement the missing functionality

export interface VNode {
  type: string | Function;
  props: Record<string, any>;
  children: (VNode | string)[];
  key?: string | number;
  dom?: HTMLElement | Text;
}

export interface Component {
  state: Record<string, any>;
  props: Record<string, any>;
  setState: (newState: Partial<Record<string, any>>) => void;
  render: () => VNode;
  componentDidMount?: () => void;
  componentDidUpdate?: (prevProps: Record<string, any>, prevState: Record<string, any>) => void;
  componentWillUnmount?: () => void;
}

export interface Store {
  state: Record<string, any>;
  subscribers: Set<Function>;
  dispatch: (action: Action) => void;
  subscribe: (callback: Function) => () => void;
  getState: () => Record<string, any>;
}

export interface Action {
  type: string;
  payload?: any;
}

export interface Reducer {
  (state: Record<string, any>, action: Action): Record<string, any>;
}

// ===== VIRTUAL DOM IMPLEMENTATION =====

/**
 * Create a virtual DOM node
 * @param type - Element type (string for HTML elements, function for components)
 * @param props - Element properties
 * @param children - Child nodes
 * @returns VNode
 */
export function createElement(type: string | Function, props: Record<string, any> = {}, ...children: (VNode | string)[]): VNode {
  const { key, ...restProps } = props;
  return {
    type,
    props: { ...restProps, children },
    children: children.flat(),
    key
  };
}

/**
 * Render virtual DOM to actual DOM
 * @param vnode - Virtual DOM node
 * @param container - DOM container
 * @returns Rendered DOM element
 */
export function render(vnode: VNode, container: HTMLElement): HTMLElement | Text {
  // Handle text nodes
  if (typeof vnode === 'string') {
    const textNode = document.createTextNode(vnode);
    container.appendChild(textNode);
    return textNode;
  }

  // Handle functional components
  if (typeof vnode.type === 'function') {
    const componentVNode = vnode.type(vnode.props);
    return render(componentVNode, container);
  }

  // Handle HTML elements
  const element = document.createElement(vnode.type as string);

  // Set up props and attributes
  Object.keys(vnode.props).forEach(key => {
    if (key === 'children') return;

    if (key.startsWith('on') && typeof vnode.props[key] === 'function') {
      // Set up event listeners
      const eventType = key.slice(2).toLowerCase();
      element.addEventListener(eventType, vnode.props[key]);
    } else if (key === 'className') {
      element.className = vnode.props[key];
    } else {
      element.setAttribute(key, vnode.props[key]);
    }
  });

  // Render children
  vnode.children.forEach(child => {
    if (typeof child === 'string') {
      element.appendChild(document.createTextNode(child));
    } else {
      render(child, element);
    }
  });

  container.appendChild(element);
  vnode.dom = element;
  return element;
}

/**
 * Diff and patch virtual DOM
 * @param oldVNode - Old virtual DOM node
 * @param newVNode - New virtual DOM node
 * @param container - DOM container
 */
export function patch(oldVNode: VNode | null, newVNode: VNode, container: HTMLElement): void {
  // If no old node, just render the new one
  if (!oldVNode) {
    render(newVNode, container);
    return;
  }

  // If nodes are the same type, update in place
  if (isSameVNode(oldVNode, newVNode)) {
    // Update props
    if (oldVNode.dom && oldVNode.dom instanceof HTMLElement) {
      const element = oldVNode.dom;

      // Remove old event listeners and props
      Object.keys(oldVNode.props).forEach(key => {
        if (key === 'children') return;
        if (key.startsWith('on')) {
          const eventType = key.slice(2).toLowerCase();
          element.removeEventListener(eventType, oldVNode.props[key]);
        }
      });

      // Add new props and event listeners
      Object.keys(newVNode.props).forEach(key => {
        if (key === 'children') return;

        if (key.startsWith('on') && typeof newVNode.props[key] === 'function') {
          const eventType = key.slice(2).toLowerCase();
          element.addEventListener(eventType, newVNode.props[key]);
        } else if (key === 'className') {
          element.className = newVNode.props[key];
        } else {
          element.setAttribute(key, newVNode.props[key]);
        }
      });

      newVNode.dom = element;
    }
  } else {
    // Replace the entire node
    if (oldVNode.dom && oldVNode.dom.parentNode) {
      const newElement = render(newVNode, document.createElement('div'));
      oldVNode.dom.parentNode.replaceChild(newElement, oldVNode.dom);
    }
  }
}

// ===== COMPONENT SYSTEM =====

/**
 * Base Component class
 */
export abstract class BaseComponent implements Component {
  state: Record<string, any> = {};
  props: Record<string, any> = {};
  private _vnode: VNode | null = null;
  private _dom: HTMLElement | null = null;

  constructor(props: Record<string, any> = {}) {
    this.props = props;
  }

  setState(newState: Partial<Record<string, any>>): void {
    const prevState = { ...this.state };
    const prevProps = { ...this.props };

    // Merge new state with existing state
    this.state = { ...this.state, ...newState };

    // Trigger re-render
    const newVNode = this.render();
    if (this._dom && this._dom.parentElement) {
      patch(this._vnode, newVNode, this._dom.parentElement);
    }
    this._vnode = newVNode;

    // Call componentDidUpdate if it exists
    if (this.componentDidUpdate) {
      this.componentDidUpdate(prevProps, prevState);
    }
  }

  abstract render(): VNode;

  componentDidMount?(): void;
  componentDidUpdate?(prevProps: Record<string, any>, prevState: Record<string, any>): void;
  componentWillUnmount?(): void;
}

/**
 * Create a functional component
 * @param component - Component function
 * @param props - Component props
 * @returns VNode
 */
export function createComponent(component: Function, props: Record<string, any>): VNode {
  // Return a VNode with the component function as the type
  // The actual rendering will be handled by the render function
  return {
    type: component,
    props,
    children: []
  };
}

// ===== STATE MANAGEMENT =====

/**
 * Create a Redux-like store
 * @param reducer - State reducer function
 * @param initialState - Initial state
 * @returns Store instance
 */
export function createStore(reducer: Reducer, initialState: Record<string, any> = {}): Store {
  let state = initialState;
  const subscribers = new Set<Function>();

  const dispatch = (action: Action) => {
    state = reducer(state, action);
    subscribers.forEach(callback => callback());
  };

  const subscribe = (callback: Function) => {
    subscribers.add(callback);
    return () => subscribers.delete(callback);
  };

  const getState = () => state;

  return {
    state,
    subscribers,
    dispatch,
    subscribe,
    getState
  };
}

/**
 * Combine multiple reducers
 * @param reducers - Object with reducer functions
 * @returns Combined reducer
 */
export function combineReducers(reducers: Record<string, Reducer>): Reducer {
  return (state: Record<string, any> = {}, action: Action) => {
    const nextState: Record<string, any> = {};

    Object.keys(reducers).forEach(key => {
      const reducer = reducers[key];
      if (reducer) {
        nextState[key] = reducer(state[key], action);
      }
    });

    return nextState;
  };
}

/**
 * Create action creator
 * @param type - Action type
 * @param payloadCreator - Function to create payload
 * @returns Action creator function
 */
export function createAction(type: string, payloadCreator?: Function) {
  return (...args: any[]) => ({
    type,
    payload: payloadCreator ? payloadCreator(...args) : args[0]
  });
}

// ===== UTILITY FUNCTIONS =====

/**
 * Check if two virtual DOM nodes are the same
 * @param vnode1 - First virtual DOM node
 * @param vnode2 - Second virtual DOM node
 * @returns True if nodes are the same
 */
export function isSameVNode(vnode1: VNode, vnode2: VNode): boolean {
  // Compare node types
  if (vnode1.type !== vnode2.type) {
    return false;
  }

  // Compare keys if they exist
  if (vnode1.key !== undefined || vnode2.key !== undefined) {
    return vnode1.key === vnode2.key;
  }

  return true;
}

/**
 * Deep clone an object
 * @param obj - Object to clone
 * @returns Cloned object
 */
export function deepClone<T>(obj: T): T {
  // Handle primitive types and null
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  // Handle arrays
  if (Array.isArray(obj)) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }

  // Handle objects
  const cloned = {} as T;
  Object.keys(obj).forEach(key => {
    (cloned as any)[key] = deepClone((obj as any)[key]);
  });

  return cloned;
}

/**
 * Debounce function execution
 * @param func - Function to debounce
 * @param wait - Wait time in milliseconds
 * @returns Debounced function
 */
export function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null = null;

  return (...args: Parameters<T>) => {
    // Cancel previous timeout on new calls
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    // Execute function after wait time
    timeoutId = setTimeout(() => {
      func(...args);
    }, wait);
  };
}

/**
 * Throttle function execution
 * @param func - Function to throttle
 * @param limit - Time limit in milliseconds
 * @returns Throttled function
 */
export function throttle<T extends (...args: any[]) => any>(func: T, limit: number): (...args: Parameters<T>) => void {
  let lastExecuted = 0;

  return (...args: Parameters<T>) => {
    const now = Date.now();

    // Execute function only if enough time has passed
    if (now - lastExecuted >= limit) {
      lastExecuted = now;
      func(...args);
    }
  };
}

// ===== EXAMPLE COMPONENTS =====

/**
 * Example Counter Component
 */
export class Counter extends BaseComponent {
  override state = { count: 0 };

  render(): VNode {
    return createElement('div', { className: 'counter' },
      createElement('h2', {}, `Count: ${this.state.count}`),
      createElement('button', { 
        onClick: () => this.setState({ count: this.state.count + 1 }) 
      }, 'Increment'),
      createElement('button', { 
        onClick: () => this.setState({ count: this.state.count - 1 }) 
      }, 'Decrement')
    );
  }
}

/**
 * Example Todo Component
 */
export class TodoApp extends BaseComponent {
  override state = {
    todos: [] as string[],
    inputValue: ''
  };

  render(): VNode {
    return createElement('div', { className: 'todo-app' },
      createElement('h1', {}, 'Todo App'),
      createElement('input', {
        value: this.state.inputValue,
        onChange: (e: Event) => this.setState({ 
          inputValue: (e.target as HTMLInputElement).value 
        }),
        placeholder: 'Add a new todo'
      }),
      createElement('button', {
        onClick: () => {
          if (this.state.inputValue.trim()) {
            this.setState({
              todos: [...this.state.todos, this.state.inputValue],
              inputValue: ''
            });
          }
        }
      }, 'Add Todo'),
      createElement('ul', {},
        ...this.state.todos.map((todo, index) =>
          createElement('li', { key: index }, todo)
        )
      )
    );
  }
}

// ===== EXAMPLE REDUCERS =====

/**
 * Example counter reducer
 */
export const counterReducer: Reducer = (state = { count: 0 }, action: Action) => {
  switch (action.type) {
    case 'INCREMENT':
      return { ...state, count: state['count'] + 1 };
    case 'DECREMENT':
      return { ...state, count: state['count'] - 1 };
    case 'RESET':
      return { ...state, count: 0 };
    default:
      return state;
  }
};

/**
 * Example todo reducer
 */
export const todoReducer: Reducer = (state = { todos: [] }, action: Action) => {
  switch (action.type) {
    case 'ADD_TODO':
      return {
        ...state,
        todos: [...state['todos'], action.payload]
      };
    case 'REMOVE_TODO':
      return {
        ...state,
        todos: state['todos'].filter((_: any, index: number) => index !== action.payload)
      };
    case 'CLEAR_TODOS':
      return { ...state, todos: [] };
    default:
      return state;
  }
};